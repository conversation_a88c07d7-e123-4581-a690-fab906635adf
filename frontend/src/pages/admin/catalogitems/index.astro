---
import AdminLayout from '@/layouts/AdminLayout.astro'
---

<AdminLayout title="Каталожные позиции">
  <div class="p-6">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0">
          Каталожные позиции
        </h1>
        <p class="text-surface-600 dark:text-surface-400 mt-1">
          Управление каталожными позициями (артикулами) от различных производителей
        </p>
      </div>
    </div>

    <!-- Основной компонент управления каталожными позициями -->
    <div id="catalog-items-manager"></div>
  </div>

  <script>
    import CatalogItemsManager from '@/components/admin/catalogitems/CatalogItemsManager.vue'
    import { createApp } from 'vue'
    import { setupVoltUI } from '@/volt/setup'

    // Создаем Vue приложение для управления каталожными позициями
    const app = createApp(CatalogItemsManager)
    setupVoltUI(app)
    app.mount('#catalog-items-manager')
  </script>
</AdminLayout>
