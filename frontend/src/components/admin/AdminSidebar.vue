<template>
  <aside class="w-64 bg-surface-section shadow-sm border-r border-surface-border min-h-full">
    <div class="p-6">
      <!-- Основные разделы -->
      <div class="mb-6">
        <h3 class="text-xs font-semibold text-surface-500 uppercase tracking-wider mb-3">
          Основное
        </h3>
        <div class="space-y-1">
          <SecondaryButton
            icon="pi pi-home"
            label="Дашборд"
            text
            class="w-full justify-start"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin') }"
            @click="navigateTo('/admin')"
          />
          <SecondaryButton
            icon="pi pi-wrench"
            label="Запчасти"
            text
            class="w-full justify-start"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/parts') }"
            @click="navigateTo('/admin/parts')"
          />
        </div>
      </div>

      <!-- Управление данными -->
      <div class="mb-6">
        <h3 class="text-xs font-semibold text-surface-500 uppercase tracking-wider mb-3">
          Управление данными
        </h3>
        <div class="space-y-1">
          <SecondaryButton
            icon="pi pi-box"
            label="Каталожные позиции"
            text
            class="w-full justify-start"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/catalogitems') }"
            @click="navigateTo('/admin/catalogitems')"
          />
          <SecondaryButton
            icon="pi pi-tags"
            label="Атрибуты"
            text
            class="w-full justify-start"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/attributes') }"
            @click="navigateTo('/admin/attributes')"
          />
        </div>
      </div>

      <!-- Система -->
      <div class="mb-6">
        <h3 class="text-xs font-semibold text-surface-500 uppercase tracking-wider mb-3">
          Система
        </h3>
        <div class="space-y-1">
          <SecondaryButton
            icon="pi pi-code"
            label="Тест UI"
            text
            class="w-full justify-start"
            :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/ui-demo') }"
            @click="navigateTo('/admin/ui-demo')"
          />
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import SecondaryButton from '@/volt/SecondaryButton.vue'

// Получаем текущий путь
const currentPath = computed(() => {
  if (typeof window !== 'undefined') {
    return window.location.pathname
  }
  return ''
})

// Методы
const navigateTo = (path: string) => {
  window.location.href = path
}

const isActive = (path: string): boolean => {
  if (path === '/admin') {
    return currentPath.value === '/admin'
  }
  return currentPath.value.startsWith(path)
}
</script>

