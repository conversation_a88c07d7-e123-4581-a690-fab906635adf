<template>
  <div class="catalog-item-card">
    <!-- Заголовок -->
    <div class="flex items-start justify-between mb-6">
      <div class="flex items-center gap-4">
        <div>
          <h2 class="text-2xl font-bold text-surface-900 dark:text-surface-0 font-mono">
            {{ item.sku }}
          </h2>
          <div class="flex items-center gap-2 mt-1">
            <VTag
              :value="item.brand?.name || 'Бренд не указан'"
              :severity="item.brand?.isOem ? 'info' : 'secondary'"
            />
            <VTag
              v-if="item.brand?.isOem"
              value="OEM"
              severity="info"
              size="small"
            />
            <VTag
              v-if="!item.isPublic"
              value="Приватная"
              severity="warning"
              size="small"
            />
          </div>
        </div>
      </div>
      
      <div class="flex gap-2">
        <VButton
          @click="$emit('edit')"
          icon="pi pi-pencil"
          severity="secondary"
          outlined
          size="small"
          label="Редактировать"
        />
        <VButton
          @click="$emit('close')"
          icon="pi pi-times"
          severity="secondary"
          text
          size="small"
        />
      </div>
    </div>

    <!-- Основная информация -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- Левая колонка -->
      <div class="space-y-4">
        <!-- Описание -->
        <VCard>
          <template #header>
            <div class="p-4 border-b border-surface-200 dark:border-surface-700">
              <h3 class="font-semibold text-surface-900 dark:text-surface-0">
                Описание
              </h3>
            </div>
          </template>
          <template #content>
            <div class="p-4">
              <p
                v-if="item.description"
                class="text-surface-700 dark:text-surface-300"
              >
                {{ item.description }}
              </p>
              <span v-else class="text-surface-400 italic">
                Описание не указано
              </span>
            </div>
          </template>
        </VCard>

        <!-- Метаданные -->
        <VCard>
          <template #header>
            <div class="p-4 border-b border-surface-200 dark:border-surface-700">
              <h3 class="font-semibold text-surface-900 dark:text-surface-0">
                Метаданные
              </h3>
            </div>
          </template>
          <template #content>
            <div class="p-4 space-y-3">
              <div class="flex justify-between">
                <span class="text-surface-600 dark:text-surface-400">Источник:</span>
                <span class="font-medium">
                  {{ item.source || 'Не указан' }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-surface-600 dark:text-surface-400">Видимость:</span>
                <VTag
                  :value="item.isPublic ? 'Публичная' : 'Приватная'"
                  :severity="item.isPublic ? 'success' : 'warning'"
                  size="small"
                />
              </div>
              <div class="flex justify-between">
                <span class="text-surface-600 dark:text-surface-400">Создана:</span>
                <span class="font-medium">
                  {{ formatDate(item.createdAt) }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-surface-600 dark:text-surface-400">Обновлена:</span>
                <span class="font-medium">
                  {{ formatDate(item.updatedAt) }}
                </span>
              </div>
            </div>
          </template>
        </VCard>
      </div>

      <!-- Правая колонка -->
      <div class="space-y-4">
        <!-- Бренд -->
        <VCard v-if="item.brand">
          <template #header>
            <div class="p-4 border-b border-surface-200 dark:border-surface-700">
              <h3 class="font-semibold text-surface-900 dark:text-surface-0">
                Информация о бренде
              </h3>
            </div>
          </template>
          <template #content>
            <div class="p-4 space-y-3">
              <div class="flex justify-between">
                <span class="text-surface-600 dark:text-surface-400">Название:</span>
                <span class="font-medium">{{ item.brand.name }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-surface-600 dark:text-surface-400">Тип:</span>
                <VTag
                  :value="item.brand.isOem ? 'OEM производитель' : 'Aftermarket'"
                  :severity="item.brand.isOem ? 'info' : 'secondary'"
                  size="small"
                />
              </div>
              <div v-if="item.brand.country" class="flex justify-between">
                <span class="text-surface-600 dark:text-surface-400">Страна:</span>
                <span class="font-medium">{{ item.brand.country }}</span>
              </div>
            </div>
          </template>
        </VCard>

        <!-- Статистика -->
        <VCard>
          <template #header>
            <div class="p-4 border-b border-surface-200 dark:border-surface-700">
              <h3 class="font-semibold text-surface-900 dark:text-surface-0">
                Статистика
              </h3>
            </div>
          </template>
          <template #content>
            <div class="p-4 space-y-3">
              <div class="flex justify-between">
                <span class="text-surface-600 dark:text-surface-400">Атрибутов:</span>
                <VTag
                  :value="item.attributes?.length || 0"
                  severity="secondary"
                  size="small"
                />
              </div>
              <div class="flex justify-between">
                <span class="text-surface-600 dark:text-surface-400">Групп применимости:</span>
                <VTag
                  :value="item.applicabilities?.length || 0"
                  :severity="(item.applicabilities?.length || 0) > 0 ? 'success' : 'secondary'"
                  size="small"
                />
              </div>
            </div>
          </template>
        </VCard>
      </div>
    </div>

    <!-- Атрибуты -->
    <VCard v-if="item.attributes?.length > 0" class="mb-6">
      <template #header>
        <div class="p-4 border-b border-surface-200 dark:border-surface-700">
          <h3 class="font-semibold text-surface-900 dark:text-surface-0">
            Атрибуты ({{ item.attributes.length }})
          </h3>
        </div>
      </template>
      <template #content>
        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="attribute in item.attributes"
              :key="attribute.id"
              class="p-3 bg-surface-50 dark:bg-surface-900 rounded border"
            >
              <div class="font-medium text-surface-900 dark:text-surface-0 mb-1">
                {{ attribute.template?.title || 'Неизвестный атрибут' }}
                <span v-if="attribute.template?.isRequired" class="text-red-500 ml-1">*</span>
              </div>
              <div class="text-lg font-semibold text-primary-600 dark:text-primary-400 mb-1">
                {{ attribute.value }}
                {{ attribute.template?.unit ? getUnitLabel(attribute.template.unit) : '' }}
              </div>
              <div class="flex items-center gap-2">
                <VTag
                  v-if="attribute.template?.group?.name"
                  :value="attribute.template.group.name"
                  severity="secondary"
                  size="small"
                />
                <span class="text-xs text-surface-500">
                  {{ getDataTypeLabel(attribute.template?.dataType) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Применимость -->
    <VCard v-if="item.applicabilities?.length > 0">
      <template #header>
        <div class="p-4 border-b border-surface-200 dark:border-surface-700">
          <h3 class="font-semibold text-surface-900 dark:text-surface-0">
            Применимость ({{ item.applicabilities.length }})
          </h3>
        </div>
      </template>
      <template #content>
        <div class="p-4">
          <div class="space-y-3">
            <div
              v-for="applicability in item.applicabilities"
              :key="applicability.id"
              class="flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"
            >
              <div class="flex-1">
                <div class="font-medium text-surface-900 dark:text-surface-0">
                  {{ applicability.part?.name || `Группа #${applicability.part?.id}` }}
                </div>
                <div class="flex items-center gap-2 mt-1">
                  <VTag
                    :value="getAccuracyLabel(applicability.accuracy)"
                    :severity="getAccuracySeverity(applicability.accuracy)"
                    size="small"
                  />
                  <span
                    v-if="applicability.notes"
                    class="text-sm text-surface-600 dark:text-surface-400"
                  >
                    {{ applicability.notes }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </VCard>
  </div>
</template>

<script setup lang="ts">
import VCard from '@/volt/Card.vue'
import VButton from '@/volt/Button.vue'
import VTag from '@/volt/Tag.vue'

// Props
interface Props {
  item: any
}

defineProps<Props>()

// Emits
interface Emits {
  (e: 'edit'): void
  (e: 'close'): void
}

defineEmits<Emits>()

// Методы
const formatDate = (dateString: string) => {
  if (!dateString) return 'Не указана'
  
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('ru-RU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    MM: 'мм',
    INCH: 'дюймы',
    FT: 'футы',
    G: 'г',
    KG: 'кг',
    T: 'т',
    LB: 'фунты',
    ML: 'мл',
    L: 'л',
    GAL: 'галлоны',
    PCS: 'шт',
    SET: 'комплект',
    PAIR: 'пара',
    BAR: 'бар',
    PSI: 'PSI',
    KW: 'кВт',
    HP: 'л.с.',
    NM: 'Н⋅м',
    RPM: 'об/мин',
    C: '°C',
    F: '°F',
    PERCENT: '%'
  }
  return labels[unit] || unit
}

const getDataTypeLabel = (dataType: string | undefined) => {
  if (!dataType) return ''
  const labels: Record<string, string> = {
    STRING: 'Строка',
    NUMBER: 'Число',
    BOOLEAN: 'Логическое',
    DATE: 'Дата',
    JSON: 'JSON'
  }
  return labels[dataType] || dataType
}

const getAccuracyLabel = (accuracy: string) => {
  const labels: Record<string, string> = {
    EXACT_MATCH: 'Точное совпадение',
    MATCH_WITH_NOTES: 'С примечаниями',
    REQUIRES_MODIFICATION: 'Требует доработки',
    PARTIAL_MATCH: 'Частичное совпадение'
  }
  return labels[accuracy] || accuracy
}

const getAccuracySeverity = (accuracy: string) => {
  const severities: Record<string, string> = {
    EXACT_MATCH: 'success',
    MATCH_WITH_NOTES: 'info',
    REQUIRES_MODIFICATION: 'warning',
    PARTIAL_MATCH: 'secondary'
  }
  return severities[accuracy] || 'secondary'
}
</script>
