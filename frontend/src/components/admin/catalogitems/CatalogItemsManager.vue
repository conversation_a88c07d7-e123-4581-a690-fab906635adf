<template>
  <div class="catalog-items-manager">
    <!-- Панель управления -->
    <VCard class="mb-6">
      <template #content>
        <div class="p-6">
          <div class="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <!-- Поиск и фильтры -->
            <div class="flex flex-col sm:flex-row gap-4 flex-1">
              <div class="flex-1">
                <VInputText
                  v-model="searchQuery"
                  placeholder="Поиск по артикулу, описанию или бренду..."
                  class="w-full"
                  @input="debouncedSearch"
                >
                  <template #prefix>
                    <i class="pi pi-search text-surface-400"></i>
                  </template>
                </VInputText>
              </div>
              
              <VAutoComplete
                v-model="selectedBrand"
                :suggestions="brandSuggestions"
                @complete="searchBrands"
                option-label="name"
                placeholder="Фильтр по бренду"
                class="w-full sm:w-64"
                @change="onBrandFilterChange"
                dropdown
              />
            </div>

            <!-- Кнопки действий -->
            <div class="flex gap-2">
              <VButton
                @click="showCreateDialog = true"
                icon="pi pi-plus"
                label="Добавить позицию"
              />
              <VButton
                @click="refreshData"
                icon="pi pi-refresh"
                severity="secondary"
                outlined
                :loading="loading"
              />
            </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Таблица каталожных позиций -->
    <CatalogItemsTable
      :items="catalogItems"
      :loading="loading"
      :total-records="totalRecords"
      :rows="pageSize"
      :first="(currentPage - 1) * pageSize"
      @page="onPageChange"
      @sort="onSort"
      @edit="onEdit"
      @delete="onDelete"
      @view-details="onViewDetails"
    />

    <!-- Диалог создания/редактирования -->
    <VDialog
      v-model:visible="showCreateDialog"
      modal
      :header="editingItem ? 'Редактировать позицию' : 'Создать позицию'"
      class="w-full max-w-2xl"
    >
      <CatalogItemForm
        :item="editingItem"
        @save="onSave"
        @cancel="onCancel"
      />
    </VDialog>

    <!-- Диалог просмотра деталей -->
    <VDialog
      v-model:visible="showDetailsDialog"
      modal
      header="Детали позиции"
      class="w-full max-w-3xl"
    >
      <CatalogItemCard
        v-if="selectedItem"
        :item="selectedItem"
        @edit="onEditFromDetails"
        @close="showDetailsDialog = false"
      />
    </VDialog>

    <!-- Диалог подтверждения удаления -->
    <VConfirmDialog />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useTrpc } from '@/composables/useTrpc'
import { useConfirm } from '@/composables/useConfirm'
import { useToast } from '@/composables/useToast'
import VCard from '@/volt/Card.vue'
import VInputText from '@/volt/InputText.vue'
import VAutoComplete from '@/volt/AutoComplete.vue'
import VButton from '@/volt/Button.vue'
import VDialog from '@/volt/Dialog.vue'
import VConfirmDialog from '@/volt/ConfirmDialog.vue'
import CatalogItemsTable from './CatalogItemsTable.vue'
import CatalogItemForm from './CatalogItemForm.vue'
import CatalogItemCard from './CatalogItemCard.vue'

// Composables
const { catalogItems: catalogItemsApi, brands, loading, error } = useTrpc()
const confirm = useConfirm()
const toast = useToast()

// Состояние
const catalogItems = ref<any[]>([])
const totalRecords = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchQuery = ref('')
const selectedBrand = ref<any>(null)
const brandSuggestions = ref<any[]>([])

// Диалоги
const showCreateDialog = ref(false)
const showDetailsDialog = ref(false)
const editingItem = ref<any>(null)
const selectedItem = ref<any>(null)

// Сортировка
const sortField = ref<string>('')
const sortOrder = ref<number>(1)

// Методы
const loadCatalogItems = async () => {
  try {
    const filters: any = {
      skip: (currentPage.value - 1) * pageSize.value,
      take: pageSize.value,
      include: {
        brand: true,
        attributes: {
          include: {
            template: true
          }
        },
        applicabilities: {
          include: {
            part: true
          }
        }
      }
    }

    // Поиск
    if (searchQuery.value.trim()) {
      filters.where = {
        OR: [
          { sku: { contains: searchQuery.value.trim(), mode: 'insensitive' } },
          { description: { contains: searchQuery.value.trim(), mode: 'insensitive' } },
          { brand: { name: { contains: searchQuery.value.trim(), mode: 'insensitive' } } }
        ]
      }
    }

    // Фильтр по бренду
    if (selectedBrand.value) {
      const brandFilter = { brandId: selectedBrand.value.id }
      if (filters.where) {
        filters.where = { AND: [filters.where, brandFilter] }
      } else {
        filters.where = brandFilter
      }
    }

    // Сортировка
    if (sortField.value) {
      filters.orderBy = { [sortField.value]: sortOrder.value === 1 ? 'asc' : 'desc' }
    } else {
      filters.orderBy = { createdAt: 'desc' }
    }

    const result = await catalogItemsApi.findMany(filters)
    if (result) {
      catalogItems.value = result
    }

    // Получаем общее количество записей
    const countResult = await catalogItemsApi.findMany({
      where: filters.where,
      select: { id: true }
    })
    if (countResult) {
      totalRecords.value = countResult.length
    }
  } catch (err) {
    console.error('Ошибка загрузки каталожных позиций:', err)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить каталожные позиции'
    })
  }
}

const searchBrands = async (event: any) => {
  try {
    const query = event.query.toLowerCase()
    const result = await brands.findMany({
      where: {
        name: { contains: query, mode: 'insensitive' }
      },
      take: 10
    })
    if (result) {
      brandSuggestions.value = result
    }
  } catch (err) {
    console.error('Ошибка поиска брендов:', err)
  }
}

// Debounced search
let searchTimeout: NodeJS.Timeout
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    loadCatalogItems()
  }, 300)
}

const onBrandFilterChange = () => {
  currentPage.value = 1
  loadCatalogItems()
}

const onPageChange = (event: any) => {
  currentPage.value = Math.floor(event.first / event.rows) + 1
  loadCatalogItems()
}

const onSort = (event: any) => {
  sortField.value = event.sortField
  sortOrder.value = event.sortOrder
  loadCatalogItems()
}

const refreshData = () => {
  loadCatalogItems()
}

const onEdit = (item: any) => {
  editingItem.value = { ...item }
  showCreateDialog.value = true
}

const onDelete = (item: any) => {
  confirm.require({
    message: `Вы уверены, что хотите удалить позицию "${item.sku}" (${item.brand?.name})?`,
    header: 'Подтверждение удаления',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Отмена',
    acceptLabel: 'Удалить',
    accept: async () => {
      try {
        await catalogItemsApi.delete({ where: { id: item.id } })
        toast.add({
          severity: 'success',
          summary: 'Успешно',
          detail: 'Позиция удалена'
        })
        loadCatalogItems()
      } catch (err) {
        console.error('Ошибка удаления:', err)
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: 'Не удалось удалить позицию'
        })
      }
    }
  })
}

const onViewDetails = (item: any) => {
  selectedItem.value = item
  showDetailsDialog.value = true
}

const onSave = async (itemData: any) => {
  try {
    if (editingItem.value) {
      await catalogItemsApi.update({
        where: { id: editingItem.value.id },
        data: itemData
      })
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Позиция обновлена'
      })
    } else {
      await catalogItemsApi.create({ data: itemData })
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Позиция создана'
      })
    }
    
    showCreateDialog.value = false
    editingItem.value = null
    loadCatalogItems()
  } catch (err) {
    console.error('Ошибка сохранения:', err)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить позицию'
    })
  }
}

const onCancel = () => {
  showCreateDialog.value = false
  editingItem.value = null
}

const onEditFromDetails = () => {
  editingItem.value = { ...selectedItem.value }
  showDetailsDialog.value = false
  showCreateDialog.value = true
}

// Инициализация
onMounted(() => {
  loadCatalogItems()
})
</script>
