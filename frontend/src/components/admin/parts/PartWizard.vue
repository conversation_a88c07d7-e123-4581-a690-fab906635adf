<template>
  <div class="part-wizard">
    <VCard>
      <template #header>
        <div
          class="flex items-center justify-between p-6 border-b border-surface-200 dark:border-surface-700"
        >
          <h2
            class="text-xl font-semibold text-surface-900 dark:text-surface-0"
          >
            Мастер создания запчасти
          </h2>
          <div
            class="flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400"
          >
            Шаг {{ currentStep }} из {{ totalSteps }}
          </div>
        </div>
      </template>

      <template #content>
        <div class="p-6">
          <!-- Индикатор прогресса -->
          <div class="mb-8">
            <div class="flex items-center justify-between">
              <div
                v-for="(step, index) in steps"
                :key="index"
                class="flex items-center"
                :class="{ 'flex-1': index < steps.length - 1 }"
              >
                <!-- Step Circle -->
                <div
                  class="w-8 h-8 px-3 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200"
                  :class="[
                    index + 1 < currentStep
                      ? 'bg-primary text-primary-contrast'
                      : index + 1 === currentStep
                      ? 'bg-primary-50 text-primary border-2 border-primary dark:bg-primary-900/20'
                      : 'bg-surface-100 text-surface-500 dark:bg-surface-800 dark:text-surface-400',
                  ]"
                >
                  <i
                    v-if="index + 1 < currentStep"
                    class="pi pi-check text-xs"
                  ></i>
                  <span v-else>{{ index + 1 }}</span>
                </div>

                <!-- Step Label -->
                <span
                  class="ml-2 text-sm font-medium transition-colors duration-200"
                  :class="[
                    index + 1 <= currentStep
                      ? 'text-primary'
                      : 'text-surface-500 dark:text-surface-400',
                  ]"
                >
                  {{ step.title }}
                </span>

                <!-- Connector Line -->
                <div
                  v-if="index < steps.length - 1"
                  class="flex-1 h-0.5 px-3 mx-4 transition-colors duration-200"
                  :class="[
                    index + 1 < currentStep
                      ? 'bg-primary'
                      : 'bg-surface-200 dark:bg-surface-700',
                  ]"
                ></div>
              </div>
            </div>
          </div>

          <!-- Содержимое шагов -->
          <div class="min-h-96">
            <!-- Шаг 1: Основная информация -->
            <div v-if="currentStep === 1" class="space-y-6">
              <h3
                class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"
              >
                Основная информация о запчасти
              </h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                  >
                    Название запчасти *
                  </label>
                  <VInputText
                    v-model="formData.name"
                    placeholder="Например: Сальник коленвала передний"
                    class="w-full p-3"
                  />
                </div>

                <div>
                  <label
                    class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                  >
                    Категория *
                  </label>
                  <div class="flex gap-2">
                    <VAutoComplete
                      v-model="selectedCategory"
                      :suggestions="categorySuggestions"
                      @complete="searchCategories"
                      option-label="name"
                      placeholder="Поиск категории..."
                      class="flex-1"
                      dropdown
                    />
                    <VButton
                      @click="showCreateCategory = true"
                      severity="secondary"
                      outlined
                      size="small"
                      label="Создать новую категорию"
                    >
                      +
                    </VButton>
                  </div>
                </div>
              </div>
            </div>

            <!-- Шаг 2: Атрибуты -->
            <div v-if="currentStep === 2" class="space-y-6">
              <SimpleAttributeManager v-model="formData.attributes" />
            </div>

            <!-- Шаг 3: Каталожные позиции -->
            <div v-if="currentStep === 3" class="space-y-6">
              <CatalogItemEditor v-model="formData.catalogItems" />
            </div>

            <!-- Шаг 4: Применимость к технике -->
            <div v-if="currentStep === 4" class="space-y-6">
              <EquipmentSelector v-model="formData.equipmentApplicabilities" />
            </div>

            <!-- Шаг 5: Подтверждение -->
            <div v-if="currentStep === 5" class="space-y-6">
              <h3
                class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"
              >
                Подтверждение создания
              </h3>

              <div class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6">
                <h4
                  class="font-medium text-surface-900 dark:text-surface-0 mb-4"
                >
                  Основная информация:
                </h4>
                <dl class="space-y-2">
                  <div class="flex">
                    <dt class="w-32 text-surface-600 dark:text-surface-400">
                      Название:
                    </dt>
                    <dd class="text-surface-900 dark:text-surface-0">
                      {{ formData.name }}
                    </dd>
                  </div>
                  <div class="flex">
                    <dt class="w-32 text-surface-600 dark:text-surface-400">
                      Категория:
                    </dt>
                    <dd class="text-surface-900 dark:text-surface-0">
                      {{ selectedCategory?.name }}
                    </dd>
                  </div>
                </dl>
              </div>

              <!-- Атрибуты -->
              <div
                v-if="formData.attributes.length > 0"
                class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6"
              >
                <h4
                  class="font-medium text-surface-900 dark:text-surface-0 mb-4"
                >
                  Атрибуты ({{ formData.attributes.length }}):
                </h4>
                <div class="space-y-3">
                  <div
                    v-for="(attribute, index) in formData.attributes"
                    :key="index"
                    class="flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-800 rounded border"
                  >
                    <div class="flex-1">
                      <div
                        class="font-medium text-surface-900 dark:text-surface-0"
                      >
                        {{
                          attribute.template?.title || attribute.templateTitle
                        }}
                        <span
                          v-if="attribute.template?.isRequired"
                          class="text-red-500 ml-1"
                          >*</span
                        >
                      </div>
                      <div
                        class="text-sm text-surface-600 dark:text-surface-400"
                      >
                        {{ attribute.value
                        }}{{
                          attribute.template?.unit || attribute.templateUnit
                            ? `
                        ${getUnitLabel(
                          attribute.template?.unit || attribute.templateUnit
                        )}`
                            : ""
                        }}
                        <span
                          v-if="
                            attribute.template?.group?.name ||
                            attribute.templateGroup
                          "
                          class="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"
                        >
                          {{
                            attribute.template?.group?.name ||
                            attribute.templateGroup
                          }}
                        </span>
                      </div>
                    </div>
                    <div class="text-xs text-surface-500 dark:text-surface-400">
                      {{
                        getDataTypeLabel(
                          attribute.template?.dataType ||
                            attribute.templateDataType
                        )
                      }}
                    </div>
                  </div>
                </div>
              </div>

              <div class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6">
                <h4
                  class="font-medium text-surface-900 dark:text-surface-0 mb-4"
                >
                  Каталожные позиции ({{ formData.catalogItems.length }}):
                </h4>
                <div class="space-y-3">
                  <div
                    v-for="(item, index) in formData.catalogItems"
                    :key="index"
                    class="flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"
                  >
                    <div class="flex-1">
                      <div class="flex items-center gap-2">
                        <span class="font-medium">
                          {{
                            item.isExisting
                              ? item.existingCatalogItem?.sku
                              : item.sku
                          }}
                        </span>
                        <span class="text-surface-600 dark:text-surface-400">
                          ({{
                            item.isExisting
                              ? item.existingCatalogItem?.brand?.name
                              : item.selectedBrand?.name
                          }})
                        </span>
                        <span
                          v-if="item.isExisting"
                          class="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"
                        >
                          Существующая
                        </span>
                        <span
                          v-else
                          class="px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"
                        >
                          Новая
                        </span>
                      </div>
                      <div
                        class="text-sm text-surface-600 dark:text-surface-400 mt-1"
                      >
                        Точность: {{ getAccuracyLabel(item.accuracy) }}
                        <span v-if="item.notes" class="ml-2"
                          >• {{ item.notes }}</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Применимость к технике -->
              <div
                v-if="formData.equipmentApplicabilities.length > 0"
                class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6"
              >
                <h4
                  class="font-medium text-surface-900 dark:text-surface-0 mb-4"
                >
                  Применимость к технике ({{
                    formData.equipmentApplicabilities.length
                  }}):
                </h4>
                <div class="space-y-3">
                  <div
                    v-for="(
                      equipment, index
                    ) in formData.equipmentApplicabilities"
                    :key="index"
                    class="flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"
                  >
                    <div class="flex-1">
                      <div class="flex items-center gap-2">
                        <span class="font-medium">
                          {{
                            equipment.isExisting
                              ? equipment.existingEquipmentModel?.name
                              : equipment.name
                          }}
                        </span>
                        <span
                          v-if="
                            equipment.selectedBrand ||
                            equipment.existingEquipmentModel?.brand
                          "
                          class="text-surface-600 dark:text-surface-400"
                        >
                          ({{
                            equipment.selectedBrand?.name ||
                            equipment.existingEquipmentModel?.brand?.name
                          }})
                        </span>
                        <span
                          v-if="equipment.isExisting"
                          class="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"
                        >
                          Существующая
                        </span>
                        <span
                          v-else
                          class="px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"
                        >
                          Новая
                        </span>
                      </div>
                      <div
                        v-if="equipment.notes"
                        class="text-sm text-surface-600 dark:text-surface-400 mt-1"
                      >
                        {{ equipment.notes }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Кнопки навигации -->
          <div
            class="flex items-center justify-between mt-8 pt-6 border-t border-surface-200 dark:border-surface-700"
          >
            <VButton
              v-if="currentStep > 1"
              @click="previousStep"
              severity="secondary"
              outlined
            >
              Назад
            </VButton>
            <div v-else></div>

            <div class="flex gap-3">
              <VButton
                v-if="currentStep < totalSteps"
                @click="nextStep"
                :disabled="!canProceed"
                label="Далее"
                outlined
              />
              <VButton
                :label="
                  mode === 'edit' ? 'Сохранить изменения' : 'Создать запчасть'
                "
                v-else
                @click="savePart"
                :loading="loading"
                :disabled="!canProceed"
              ></VButton>
            </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Сообщение об ошибке -->
    <VMessage v-if="error" severity="error" class="mt-4">
      {{ error }}
    </VMessage>

    <!-- Диалоги быстрого создания -->
    <QuickCreateCategory
      v-model:visible="showCreateCategory"
      @created="onCategoryCreated"
    />

    <QuickCreateBrand
      v-model:visible="showCreateBrand"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useTrpc } from "@/composables/useTrpc";
import VCard from "@/volt/Card.vue";
import VButton from "@/volt/Button.vue";
import VInputText from "@/volt/InputText.vue";
import VAutoComplete from "@/volt/AutoComplete.vue";
import VMessage from "@/volt/Message.vue";
import QuickCreateCategory from "./QuickCreateCategory.vue";
import QuickCreateBrand from "./QuickCreateBrand.vue";
import SimpleAttributeManager from "./SimpleAttributeManager.vue";
import EquipmentSelector from "./EquipmentSelector.vue";
import CatalogItemEditor from "./CatalogItemEditor.vue";

// Props для поддержки редактирования
interface Props {
  part?: any; // Запчасть для редактирования (если null - создание новой)
  mode?: "create" | "edit";
}

const props = withDefaults(defineProps<Props>(), {
  part: null,
  mode: "create",
});

// Emits
interface Emits {
  (e: "created", part: any): void;
  (e: "updated", part: any): void;
}

const emit = defineEmits<Emits>();

// Интерфейсы
interface CatalogItemForm {
  // Для создания новой позиции
  sku: string;
  brandId: number | "";
  selectedBrand: any;
  description: string;

  // Для поиска существующей позиции
  isExisting?: boolean; // true если выбрана существующая позиция
  existingCatalogItem?: any; // выбранная существующая позиция

  // Уровень точности применимости
  accuracy:
    | "EXACT_MATCH"
    | "MATCH_WITH_NOTES"
    | "REQUIRES_MODIFICATION"
    | "PARTIAL_MATCH";
  notes?: string; // примечания для accuracy
}

interface AttributeForm {
  id?: number; // ID существующего атрибута (для редактирования)
  templateId: number; // ID шаблона
  value: string; // Значение атрибута

  // Информация из шаблона (для отображения)
  template?: {
    id: number;
    name: string;
    title: string;
    description?: string;
    dataType: string;
    unit?: string;
    group?: {
      id: number;
      name: string;
    };
  };

  // Альтернативные поля для новых атрибутов (до сохранения)
  templateTitle?: string;
  templateDataType?: string;
  templateUnit?: string;
  templateGroup?: string;
  templateDescription?: string;
}

interface EquipmentApplicabilityForm {
  // Для создания новой модели
  name: string;
  selectedBrand: any;

  // Для поиска существующей модели
  isExisting?: boolean; // true если выбрана существующая модель
  existingEquipmentModel?: any; // выбранная существующая модель

  // Примечания
  notes?: string;
}

interface PartForm {
  name: string;
  attributes: AttributeForm[];
  catalogItems: CatalogItemForm[];
  equipmentApplicabilities: EquipmentApplicabilityForm[];
}

// Шаги мастера
const steps = [
  { title: "Основная информация", key: "basic" },
  { title: "Атрибуты", key: "attributes" },
  { title: "Каталожные позиции", key: "catalog" },
  { title: "Применимость к технике", key: "equipment" },
  { title: "Подтверждение", key: "confirm" },
];

const totalSteps = steps.length;
const currentStep = ref(1);

// Данные формы
const formData = ref<PartForm>({
  name: "",
  attributes: [],
  catalogItems: [],
  equipmentApplicabilities: [],
});

// tRPC клиент
const {
  loading,
  error,
  clearError,
  partCategories,
  parts,
  catalogItems,
  equipmentModels,
  partAttributes,
  client,
} = useTrpc();

// Удалены опции точности - теперь в CatalogItemEditor

// Удалены опции для типа каталожной позиции - теперь в CatalogItemEditor

// Удалены методы фильтрации точности - теперь в CatalogItemEditor

// Данные для автокомплита
const selectedCategory = ref<any>(null);
const categorySuggestions = ref<any[]>([]);

// Данные для автокомплита точности применимости (объявляется после accuracyOptions)

// Диалоги быстрого создания
const showCreateCategory = ref(false);
const showCreateBrand = ref(false);

// Обработчики быстрого создания
const onCategoryCreated = (category: any) => {
  selectedCategory.value = category;
  categorySuggestions.value = [category, ...categorySuggestions.value];
};

// Удален метод onBrandCreated - теперь в CatalogItemEditor

// Поиск категорий
const searchCategories = async (event: any) => {
  const query = event.query.toLowerCase();
  const categories = await partCategories.findMany({
    where: {
      name: {
        contains: query,
        mode: "insensitive",
      },
    },
    take: 10,
  });

  if (categories && Array.isArray(categories)) {
    categorySuggestions.value = categories;
  }
};

// Удалены методы поиска - теперь в CatalogItemEditor

// Вычисляемые свойства
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 1:
      return formData.value.name.trim() && selectedCategory.value;
    case 2:
      // Атрибуты не обязательны, можно пропустить
      return true;
    case 3:
      return (
        formData.value.catalogItems.length > 0 &&
        formData.value.catalogItems.every((item) => {
          if (item.isExisting) {
            return item.existingCatalogItem && item.accuracy;
          } else {
            return item.sku.trim() && item.selectedBrand && item.accuracy;
          }
        })
      );
    case 4:
      // Техника не обязательна, можно пропустить
      return formData.value.equipmentApplicabilities.every((equipment) => {
        if (equipment.isExisting) {
          return equipment.existingEquipmentModel;
        } else {
          return equipment.name.trim();
        }
      });
    case 5:
      return true;
    default:
      return false;
  }
});

// Методы навигации
const nextStep = () => {
  if (currentStep.value < totalSteps && canProceed.value) {
    currentStep.value++;
    clearError();
  }
};

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
    clearError();
  }
};

// Удалены методы работы с каталожными позициями - теперь в CatalogItemEditor

// Создание или обновление запчасти
const savePart = async () => {
  if (!canProceed.value) return;

  try {
    clearError();

    const partData = {
      name: formData.value.name,
      partCategoryId: typeof selectedCategory.value === 'object' 
        ? selectedCategory.value.id 
        : selectedCategory.value,
      level: props.part?.level || 0,
      path: props.part?.path || "/",
    };

    let partResult;
    if (props.mode === "edit" && props.part) {
      // Обновление существующей запчасти
      partResult = await parts.update({
        where: { id: props.part.id },
        data: partData,
      });
    } else {
      // Создание новой запчасти
      partResult = await parts.create({
        data: partData,
      });
    }

    if (!partResult) {
      throw new Error(
        `Не удалось ${props.mode === "edit" ? "обновить" : "создать"} запчасть`
      );
    }

    // Для новых запчастей загружаем существующие атрибуты (если есть)
    if (props.mode === "create" && (partResult as any)?.id) {
      await loadExistingAttributes((partResult as any).id);
    }

    // Создаем/обновляем атрибуты запчасти
    for (const attribute of formData.value.attributes) {
      if (!attribute.templateId || !attribute.value || !String(attribute.value).trim()) continue;

      if (attribute.id) {
        // Обновляем существующий атрибут
        await partAttributes.update({
          id: attribute.id,
          value: String(attribute.value).trim(),
        });
      } else {
        // Создаем новый атрибут
        try {
          const savedAttribute = await partAttributes.create({
            partId: (partResult as any)?.id,
            templateId: attribute.templateId,
            value: String(attribute.value).trim(),
          });

          if (savedAttribute) {
            attribute.id = (savedAttribute as any).id;
          }
        } catch (error: any) {
          // Если атрибут уже существует, пропускаем
          if (error.message?.includes("уже существует")) {
            console.warn(
              `Атрибут с шаблоном ${attribute.templateId} уже существует, пропускаем`
            );
            continue;
          }
          throw error;
        }
      }
    }

    // Создаем каталожные позиции и связи
    for (const catalogItem of formData.value.catalogItems) {
      let catalogItemId: number;

      if (catalogItem.isExisting && catalogItem.existingCatalogItem) {
        // Используем существующую каталожную позицию
        catalogItemId = catalogItem.existingCatalogItem.id;
      } else {
        // Создаем новую каталожную позицию
        let brandId: number;

        // Правильно извлекаем brandId
        if (typeof catalogItem.selectedBrand === 'object' && catalogItem.selectedBrand?.id) {
          brandId = catalogItem.selectedBrand.id;
        } else if (typeof catalogItem.selectedBrand === 'number') {
          brandId = catalogItem.selectedBrand;
        } else if (typeof catalogItem.brandId === 'number') {
          brandId = catalogItem.brandId;
        } else {
          throw new Error(`Не выбран бренд для каталожной позиции ${catalogItem.sku}`);
        }

        const catalogResult = await catalogItems.create({
          data: {
            sku: catalogItem.sku.toUpperCase().trim(),
            brandId: brandId,
            description: catalogItem.description || undefined,
            isPublic: true,
          },
        });

        if (!catalogResult) {
          throw new Error(
            `Не удалось создать каталожную позицию ${catalogItem.sku}`
          );
        }

        catalogItemId = (catalogResult as any)?.id;
      }

      // Создаем или обновляем связь между запчастью и каталожной позицией (upsert)
      try {
        await client.crud.partApplicability.upsert.mutate({
          where: {
            partId_catalogItemId: {
              partId: (partResult as any)?.id,
              catalogItemId: catalogItemId,
            },
          },
          update: {
            accuracy: catalogItem.accuracy,
            notes: catalogItem.notes || undefined,
          },
          create: {
            partId: (partResult as any)?.id,
            catalogItemId: catalogItemId,
            accuracy: catalogItem.accuracy,
            notes: catalogItem.notes || undefined,
          },
        });
      } catch (error: any) {
        // Если upsert не поддерживается, пробуем обычное создание
        console.warn('Upsert не поддерживается, пробуем создание:', error);
        try {
          await client.crud.partApplicability.create.mutate({
            data: {
              partId: (partResult as any)?.id,
              catalogItemId: catalogItemId,
              accuracy: catalogItem.accuracy,
              notes: catalogItem.notes || undefined,
            },
          });
        } catch (createError: any) {
          if (createError.message?.includes('Unique constraint failed')) {
            // Если запись уже существует, обновляем её
            const existing = await client.crud.partApplicability.findFirst.query({
              where: {
                partId: (partResult as any)?.id,
                catalogItemId: catalogItemId,
              },
            });

            if (existing) {
              await client.crud.partApplicability.update.mutate({
                where: { id: existing.id },
                data: {
                  accuracy: catalogItem.accuracy,
                  notes: catalogItem.notes || undefined,
                },
              });
            }
          } else {
            throw createError;
          }
        }
      }
    }

    // Создаем связи с техникой
    for (const equipment of formData.value.equipmentApplicabilities) {
      let equipmentModelId: string;

      if (equipment.isExisting && equipment.existingEquipmentModel) {
        // Используем существующую модель техники
        equipmentModelId = equipment.existingEquipmentModel.id;
      } else {
        // Создаем новую модель техники
        const equipmentData: any = {
          name: equipment.name.trim(),
        };

        // Добавляем бренд если выбран
        if (equipment.selectedBrand) {
          equipmentData.brandId = typeof equipment.selectedBrand === 'object'
            ? equipment.selectedBrand.id
            : equipment.selectedBrand;
        }

        const equipmentResult = await equipmentModels.create({
          data: equipmentData,
        });

        if (!equipmentResult) {
          throw new Error(
            `Не удалось создать модель техники ${equipment.name}`
          );
        }

        equipmentModelId = (equipmentResult as any)?.id;
      }

      // Создаем или обновляем связь между запчастью и моделью техники (upsert)
      try {
        await client.crud.equipmentApplicability.upsert.mutate({
          where: {
            partId_equipmentModelId: {
              partId: (partResult as any)?.id,
              equipmentModelId: equipmentModelId,
            },
          },
          update: {
            notes: equipment.notes || undefined,
          },
          create: {
            partId: (partResult as any)?.id,
            equipmentModelId: equipmentModelId,
            notes: equipment.notes || undefined,
          },
        });
      } catch (error: any) {
        // Если upsert не поддерживается, пробуем обычное создание
        console.warn('Upsert не поддерживается, пробуем создание:', error);
        try {
          await client.crud.equipmentApplicability.create.mutate({
            data: {
              partId: (partResult as any)?.id,
              equipmentModelId: equipmentModelId,
              notes: equipment.notes || undefined,
            },
          });
        } catch (createError: any) {
          if (createError.message?.includes('Unique constraint failed')) {
            // Если запись уже существует, обновляем её
            const existing = await client.crud.equipmentApplicability.findFirst.query({
              where: {
                partId: (partResult as any)?.id,
                equipmentModelId: equipmentModelId,
              },
            });

            if (existing) {
              await client.crud.equipmentApplicability.update.mutate({
                where: { id: existing.id },
                data: {
                  notes: equipment.notes || undefined,
                },
              });
            }
          } else {
            throw createError;
          }
        }
      }
    }

    // Успешное сохранение
    if (props.mode === "edit") {
      emit("updated", partResult);
    } else {
      emit("created", partResult);
    }

    // Сброс формы только при создании
    if (props.mode === "create") {
      resetForm();
    }
  } catch (err: any) {
    console.error(
      `Ошибка ${props.mode === "edit" ? "обновления" : "создания"} запчасти:`,
      err
    );
  }
};

// Сброс формы
const resetForm = () => {
  formData.value = {
    name: "",
    attributes: [],
    catalogItems: [],
    equipmentApplicabilities: [],
  };
  selectedCategory.value = null;
  currentStep.value = 1;
};

// Вспомогательные функции для отображения
const getDataTypeLabel = (dataType: string | undefined) => {
  if (!dataType) return "";
  const labels: Record<string, string> = {
    STRING: "Строка",
    NUMBER: "Число",
    BOOLEAN: "Логическое",
    DATE: "Дата",
    JSON: "JSON",
  };
  return labels[dataType] || dataType;
};

const getUnitLabel = (unit: string | undefined) => {
  if (!unit) return "";
  const labels: Record<string, string> = {
    MM: "мм",
    INCH: "дюймы",
    FT: "футы",
    G: "г",
    KG: "кг",
    T: "т",
    LB: "фунты",
    ML: "мл",
    L: "л",
    GAL: "галлоны",
    PCS: "шт",
    SET: "комплект",
    PAIR: "пара",
    BAR: "бар",
    PSI: "PSI",
    KW: "кВт",
    HP: "л.с.",
    NM: "Н⋅м",
    RPM: "об/мин",
    C: "°C",
    F: "°F",
    PERCENT: "%",
  };
  return labels[unit] || unit;
};

const getAccuracyLabel = (accuracy: string) => {
  const labels: Record<string, string> = {
    EXACT_MATCH: "Точное совпадение",
    MATCH_WITH_NOTES: "С примечаниями",
    REQUIRES_MODIFICATION: "Требует доработки",
    PARTIAL_MATCH: "Частичное совпадение",
  };
  return labels[accuracy] || accuracy;
};

// Загрузка существующих атрибутов запчасти
const loadExistingAttributes = async (partId: number) => {
  try {
    const attributes = await partAttributes.findByPartId({ partId });
    if (attributes && Array.isArray(attributes)) {
      formData.value.attributes = attributes.map((attr: any) => ({
        id: attr.id,
        templateId: attr.templateId,
        value: attr.value,
        template: attr.template,
        // Для отображения
        templateTitle: attr.template?.title,
        templateDataType: attr.template?.dataType,
        templateUnit: attr.template?.unit,
        templateGroup: attr.template?.group?.name,
        templateDescription: attr.template?.description,
      }));
    }
  } catch (error) {
    console.warn("Не удалось загрузить атрибуты запчасти:", error);
  }
};

// Загрузка существующих каталожных позиций
const loadExistingCatalogItems = async (partId: number) => {
  try {
    const applicabilities = await client.crud.partApplicability.findMany.query({
      where: { partId },
      include: {
        catalogItem: {
          include: {
            brand: true
          }
        }
      }
    });

    if (applicabilities) {
      formData.value.catalogItems = applicabilities.map((app: any) => ({
        isExisting: true,
        existingCatalogItem: app.catalogItem,
        accuracy: app.accuracy,
        notes: app.notes || "",
        // Поля для новых позиций (пустые)
        sku: "",
        brandId: "",
        selectedBrand: null,
        description: "",
      }));
    }
  } catch (error) {
    console.warn("Не удалось загрузить каталожные позиции:", error);
  }
};

// Загрузка существующей применимости к технике
const loadExistingEquipmentApplicabilities = async (partId: number) => {
  try {
    const applicabilities = await client.crud.equipmentApplicability.findMany.query({
      where: { partId },
      include: {
        equipmentModel: {
          include: {
            brand: true
          }
        }
      }
    });

    if (applicabilities) {
      formData.value.equipmentApplicabilities = applicabilities.map((app: any) => ({
        isExisting: true,
        existingEquipmentModel: app.equipmentModel,
        notes: app.notes || "",
        // Поля для новых моделей (пустые)
        name: "",
        selectedBrand: null,
      }));
    }
  } catch (error) {
    console.warn("Не удалось загрузить применимость к технике:", error);
  }
};

// Загрузка данных для редактирования
const loadPartData = async () => {
  if (props.part && props.mode === "edit") {
    formData.value.name = props.part.name || "";

    // Устанавливаем категорию
    if (props.part.partCategory) {
      selectedCategory.value = props.part.partCategory;
    }

    // Загружаем все связанные данные
    if (props.part.id) {
      await Promise.all([
        loadExistingAttributes(props.part.id),
        loadExistingCatalogItems(props.part.id),
        loadExistingEquipmentApplicabilities(props.part.id)
      ]);
    }
  }
};

// Watchers
watch(
  () => props.part,
  async () => {
    if (props.mode === "edit") {
      await loadPartData();
    }
  },
  { immediate: true }
);

// Инициализация
onMounted(async () => {
  if (props.mode === "edit" && props.part) {
    await loadPartData();
  }
});
</script>
