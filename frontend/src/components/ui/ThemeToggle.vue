<template>
  <div class="theme-toggle">
    <!-- Простая кнопка переключения -->
    <button
      v-if="mode === 'toggle'"
      @click="toggleTheme"
      :class="buttonClass"
      class="p-2 rounded-md text-surface-700 dark:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-200 transition-colors"
      :title="`Переключить тему (текущая: ${themeName})`"
    >
      <component :is="themeIconComponent" :size="20" />
      <span v-if="showLabel" class="ml-2">{{ themeName }}</span>
    </button>

    <!-- Выпадающее меню с выбором темы -->
    <div v-else-if="mode === 'menu'" class="relative">
      <button
        @click="toggleMenu"
        :class="buttonClass"
        class="p-2 rounded-md text-surface-700 dark:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-200 transition-colors flex items-center"
        :title="`Выбрать тему (текущая: ${themeName})`"
      >
        <component :is="themeIconComponent" :size="20" />
        <span v-if="showLabel" class="ml-2">{{ themeName }}</span>
      </button>
      
      <!-- Выпадающее меню -->
      <div
        v-if="showMenu"
        class="absolute right-0 mt-2 w-48 bg-surface-0 dark:bg-surface-100 rounded-md shadow-lg border border-surface-200 dark:border-surface-700 z-50"
        @click.stop
      >
        <div class="py-1">
          <button
            v-for="theme in themes"
            :key="theme.value"
            @click="selectTheme(theme.value)"
            class="flex items-center w-full px-4 py-2 text-sm text-surface-700 dark:text-surface-300 hover:bg-surface-50 dark:hover:bg-surface-200 transition-colors"
            :class="{
              'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400': currentTheme === theme.value
            }"
          >
            <component :is="theme.icon" :size="16" class="mr-3" />
            <span>{{ theme.label }}</span>
            <Check
              v-if="currentTheme === theme.value"
              :size="16"
              class="ml-auto text-primary-600 dark:text-primary-400"
            />
          </button>
        </div>
      </div>
    </div>

    <!-- Группа кнопок -->
    <div v-else-if="mode === 'buttons'" class="flex rounded-md border border-surface-200 dark:border-surface-700 overflow-hidden">
      <button
        v-for="theme in themes"
        :key="theme.value"
        @click="selectTheme(theme.value)"
        class="flex items-center px-3 py-2 text-sm transition-colors border-r border-surface-200 dark:border-surface-700 last:border-r-0"
        :class="{
          'bg-primary-500 text-white': currentTheme === theme.value,
          'bg-surface-0 dark:bg-surface-100 text-surface-700 dark:text-surface-300 hover:bg-surface-50 dark:hover:bg-surface-200': currentTheme !== theme.value
        }"
        :title="`Выбрать ${theme.label.toLowerCase()} тему`"
      >
        <component :is="theme.icon" :size="16" />
        <span v-if="showLabel" class="ml-2">{{ theme.label }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTheme, type ThemeType } from '@/composables/useTheme'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import { Sun, Moon, Monitor, Check } from 'lucide-vue-next'

interface Props {
  mode?: 'toggle' | 'menu' | 'buttons'
  showLabel?: boolean
  buttonClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'toggle',
  showLabel: false,
  buttonClass: ''
})

// Используем composable темы
const {
  currentTheme,
  activeTheme,
  isDark,
  isLight,
  isSystem,
  themeIcon,
  themeName,
  setTheme,
  toggleTheme
} = useTheme()

// Локальное состояние для меню
const showMenu = ref(false)

// Компонент иконки для текущей темы
const themeIconComponent = computed(() => {
  switch (currentTheme.value) {
    case 'light':
      return Sun
    case 'dark':
      return Moon
    case 'system':
      return Monitor
    default:
      return Monitor
  }
})

// Список доступных тем с Lucide иконками
const themes = [
  { value: 'light' as ThemeType, label: 'Светлая', icon: Sun },
  { value: 'dark' as ThemeType, label: 'Темная', icon: Moon },
  { value: 'system' as ThemeType, label: 'Системная', icon: Monitor }
]

// Методы
const toggleMenu = () => {
  showMenu.value = !showMenu.value
}

const selectTheme = (theme: ThemeType) => {
  setTheme(theme)
  showMenu.value = false
}

// Закрытие меню при клике вне его
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.theme-toggle')) {
    showMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.theme-toggle {
  position: relative;
}

/* Анимация для плавного появления меню */
.theme-toggle .absolute {
  animation: fadeIn 0.15s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
