<template>
  <div class="min-h-screen flex items-center justify-center bg-surface-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center">
          <img class="h-12 w-12" src="/favicon.svg" alt="PartTec" />
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-surface-900">
          Регистрация в PartTec
        </h2>
        <p class="mt-2 text-center text-sm text-surface-600">
          Создайте аккаунт для доступа к системе
        </p>
      </div>

      <!-- Форма -->
      <Card class="mt-8">
        <template #content>
          <form class="space-y-6" @submit.prevent="handleSubmit">
            <!-- Общая ошибка -->
            <Message
              v-if="generalError"
              severity="error"
              :closable="false"
            >
              {{ generalError }}
            </Message>

            <!-- Имя -->
            <div>
              <InputText
                id="name"
                v-model="formData.name"
                type="text"
                autocomplete="name"
                class="w-full"
                :invalid="!!fieldErrors.name"
                @blur="validateField('name')"
                @input="clearFieldError('name')"
              />
              <label for="name">Полное имя</label>
            </div>
            <small v-if="fieldErrors.name" class="text-red-500">
              {{ fieldErrors.name }}
            </small>

            <!-- Email -->
            <div>
              <InputText
                id="email"
                v-model="formData.email"
                type="email"
                autocomplete="email"
                class="w-full"
                :invalid="!!fieldErrors.email"
                @blur="validateField('email')"
                @input="clearFieldError('email')"
              />
              <label for="email">Email адрес</label>
            </div>
            <small v-if="fieldErrors.email" class="text-red-500">
              {{ fieldErrors.email }}
            </small>

            <!-- Пароль -->
            <div>
              <Password
                id="password"
                v-model="formData.password"
                autocomplete="new-password"
                class="w-full"
                :invalid="!!fieldErrors.password"
                :feedback="false"
                toggle-mask
                @blur="validateField('password')"
                @input="clearFieldError('password')"
              />
              <label for="password">Пароль</label>
            </div>
            <small v-if="fieldErrors.password" class="text-red-500">
              {{ fieldErrors.password }}
            </small>

            <!-- Подтверждение пароля -->
            <div>
              <Password
                id="confirmPassword"
                v-model="formData.confirmPassword"
                autocomplete="new-password"
                class="w-full"
                :invalid="!!fieldErrors.confirmPassword"
                :feedback="false"
                toggle-mask
                @blur="validateField('confirmPassword')"
                @input="clearFieldError('confirmPassword')"
              />
              <label for="confirmPassword">Подтвердите пароль</label>
            </div>
            <small v-if="fieldErrors.confirmPassword" class="text-red-500">
              {{ fieldErrors.confirmPassword }}
            </small>

            <!-- Роль пользователя -->
            <div>
              <Select
                id="role"
                v-model="formData.role"
                :options="roleOptions"
                option-label="label"
                option-value="value"
                class="w-full"
                :invalid="!!fieldErrors.role"
                @change="clearFieldError('role')"
              />
              <label for="role">Тип аккаунта</label>
            </div>
            <small v-if="fieldErrors.role" class="text-red-500">
              {{ fieldErrors.role }}
            </small>

            <!-- Согласие с условиями -->
            <div class="flex items-start">
              <Checkbox
                id="acceptTerms"
                v-model="formData.acceptTerms"
                :binary="true"
                :invalid="!!fieldErrors.acceptTerms"
                @change="clearFieldError('acceptTerms')"
              />
              <label for="acceptTerms" class="ml-2 block text-sm text-surface-700">
                Я принимаю 
                <a href="/terms" class="font-medium text-primary-600 hover:text-primary-500">
                  условия использования
                </a>
                и 
                <a href="/privacy" class="font-medium text-primary-600 hover:text-primary-500">
                  политику конфиденциальности
                </a>
              </label>
            </div>
            <small v-if="fieldErrors.acceptTerms" class="text-red-500">
              {{ fieldErrors.acceptTerms }}
            </small>

            <!-- Submit button -->
            <div>
              <Button
                type="submit"
                :disabled="isSubmitting || !isFormValid"
                :loading="isSubmitting"
                label="Зарегистрироваться"
                class="w-full"
                size="large"
              />
            </div>

            <!-- Ссылка на вход -->
            <div class="text-center">
              <p class="text-sm text-surface-600">
                Уже есть аккаунт?
                <a href="/admin/login" class="font-medium text-primary-600 hover:text-primary-500">
                  Войти в систему
                </a>
              </p>
            </div>
          </form>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useAuth, type RegisterFormData } from '@/composables/useAuth'
import Button from '@/volt/Button.vue'
import Card from '@/volt/Card.vue'
import InputText from '@/volt/InputText.vue'
import Password from '@/volt/Password.vue'
import Message from '@/volt/Message.vue'
import Checkbox from '@/volt/Checkbox.vue'
import Select from '@/volt/Select.vue'

// Composables
const { signUp, RegisterFormSchema } = useAuth()

// Локальное состояние
const isSubmitting = ref(false)
const generalError = ref('')

// Опции для выбора роли
const roleOptions = [
  { label: 'Пользователь', value: 'USER' },
  { label: 'Магазин запчастей', value: 'SHOP' },
  // ADMIN роль может назначать только существующий админ
]

// Данные формы
const formData = reactive<RegisterFormData>({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: 'USER',
  acceptTerms: false
})

// Ошибки полей
const fieldErrors = reactive<Record<string, string>>({})

// Вычисляемые свойства
const isFormValid = computed(() => {
  return formData.name && 
         formData.email && 
         formData.password && 
         formData.confirmPassword &&
         formData.acceptTerms &&
         Object.keys(fieldErrors).length === 0
})

// Методы валидации
const validateField = (fieldName: keyof RegisterFormData) => {
  const result = RegisterFormSchema.safeParse(formData)
  
  if (!result.success) {
    const fieldError = result.error.issues.find(issue => 
      issue.path.includes(fieldName)
    )
    
    if (fieldError) {
      fieldErrors[fieldName] = fieldError.message
    }
  } else {
    delete fieldErrors[fieldName]
  }
}

const clearFieldError = (fieldName: string) => {
  delete fieldErrors[fieldName]
  generalError.value = ''
}

// Обработка отправки формы
const handleSubmit = async () => {
  // Очищаем предыдущие ошибки
  Object.keys(fieldErrors).forEach(key => delete fieldErrors[key])
  generalError.value = ''

  // Валидируем форму
  const validationResult = RegisterFormSchema.safeParse(formData)
  if (!validationResult.success) {
    validationResult.error.issues.forEach(issue => {
      const fieldName = issue.path[0] as string
      fieldErrors[fieldName] = issue.message
    })
    return
  }

  isSubmitting.value = true

  try {
    const result = await signUp(formData)

    if (result.error) {
      // Обрабатываем различные типы ошибок
      if (result.error.message.includes('already exists')) {
        generalError.value = 'Пользователь с таким email уже существует'
      } else if (result.error.message.includes('Invalid email')) {
        generalError.value = 'Некорректный email адрес'
      } else {
        generalError.value = result.error.message || 'Ошибка регистрации'
      }
    } else {
      // Успешная регистрация
      console.log('✅ Успешная регистрация')
      
      // Перенаправляем на дашборд или страницу подтверждения
      setTimeout(() => {
        window.location.href = '/admin'
      }, 100)
    }
  } catch (error) {
    console.error('Registration error:', error)
    generalError.value = 'Произошла ошибка при регистрации. Попробуйте позже.'
  } finally {
    isSubmitting.value = false
  }
}
</script>
