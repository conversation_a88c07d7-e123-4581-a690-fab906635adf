/**
 * Composable для управления темами приложения
 * Поддерживает светлую и темную темы с сохранением в localStorage
 */

import { ref, computed, watch, onMounted } from 'vue'

export type ThemeType = 'light' | 'dark' | 'system'

const STORAGE_KEY = 'parttec-theme'

// Глобальное состояние темы
const currentTheme = ref<ThemeType>('system')
const systemPrefersDark = ref(false)

// Вычисляемое значение активной темы
const activeTheme = computed(() => {
  if (currentTheme.value === 'system') {
    return systemPrefersDark.value ? 'dark' : 'light'
  }
  return currentTheme.value
})

// Функция для применения темы к DOM
const applyTheme = (theme: 'light' | 'dark') => {
  if (typeof document !== 'undefined') {
    const root = document.documentElement

    if (theme === 'dark') {
      root.setAttribute('data-theme', 'dark')
      root.classList.add('dark')
    } else {
      root.removeAttribute('data-theme')
      root.classList.remove('dark')
    }
  }
}

// Функция для определения системной темы
const updateSystemTheme = () => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    systemPrefersDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }
}

// Функция для загрузки темы из localStorage
const loadTheme = (): ThemeType => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (stored && ['light', 'dark', 'system'].includes(stored)) {
      return stored as ThemeType
    }
  }
  return 'system'
}

// Функция для сохранения темы в localStorage
const saveTheme = (theme: ThemeType) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(STORAGE_KEY, theme)
  }
}

export const useTheme = () => {
  // Инициализация при первом использовании
  onMounted(() => {
    // Загружаем сохраненную тему
    currentTheme.value = loadTheme()

    // Определяем системную тему
    updateSystemTheme()

    // Слушаем изменения системной темы
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handleChange = (e: MediaQueryListEvent) => {
        systemPrefersDark.value = e.matches
      }

      mediaQuery.addEventListener('change', handleChange)

      // Очистка при размонтировании
      return () => {
        mediaQuery.removeEventListener('change', handleChange)
      }
    }
  })

  // Отслеживаем изменения активной темы и применяем их
  watch(activeTheme, (newTheme) => {
    applyTheme(newTheme)
  }, { immediate: true })

  // Отслеживаем изменения выбранной темы и сохраняем их
  watch(currentTheme, (newTheme) => {
    saveTheme(newTheme)
  })

  // Функция для установки темы
  const setTheme = (theme: ThemeType) => {
    currentTheme.value = theme
  }

  // Функция для переключения между светлой и темной темой
  const toggleTheme = () => {
    if (currentTheme.value === 'system') {
      // Если система, переключаем на противоположную системной
      setTheme(systemPrefersDark.value ? 'light' : 'dark')
    } else if (currentTheme.value === 'light') {
      setTheme('dark')
    } else {
      setTheme('light')
    }
  }

  // Функция для сброса к системной теме
  const resetToSystem = () => {
    setTheme('system')
  }

  // Вычисляемые значения для удобства
  const isDark = computed(() => activeTheme.value === 'dark')
  const isLight = computed(() => activeTheme.value === 'light')
  const isSystem = computed(() => currentTheme.value === 'system')

  // Иконки для тем (Lucide)
  const themeIcon = computed(() => {
    switch (currentTheme.value) {
      case 'light':
        return 'Sun'
      case 'dark':
        return 'Moon'
      case 'system':
        return 'Monitor'
      default:
        return 'Monitor'
    }
  })

  // Название темы для отображения
  const themeName = computed(() => {
    switch (currentTheme.value) {
      case 'light':
        return 'Светлая'
      case 'dark':
        return 'Темная'
      case 'system':
        return 'Системная'
      default:
        return 'Системная'
    }
  })

  return {
    // Состояние (только для чтения)
    currentTheme: computed(() => currentTheme.value),
    activeTheme,
    systemPrefersDark: computed(() => systemPrefersDark.value),

    // Вычисляемые значения
    isDark,
    isLight,
    isSystem,
    themeIcon,
    themeName,

    // Методы
    setTheme,
    toggleTheme,
    resetToSystem
  }
}

// Глобальная функция для инициализации темы (для использования в Astro)
export const initTheme = () => {
  if (typeof window === 'undefined') return

  const theme = localStorage.getItem(STORAGE_KEY) || 'system'
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  const shouldBeDark = theme === 'dark' || (theme === 'system' && systemPrefersDark)

  if (shouldBeDark) {
    document.documentElement.setAttribute('data-theme', 'dark')
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.removeAttribute('data-theme')
    document.documentElement.classList.remove('dark')
  }
}
