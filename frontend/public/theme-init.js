// Глобальная инициализация темы для предотвращения мигания
(function() {
  const STORAGE_KEY = 'parttec-theme';
  
  function initTheme() {
    const theme = localStorage.getItem(STORAGE_KEY) || 'system';
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const shouldBeDark = theme === 'dark' || (theme === 'system' && systemPrefersDark);

    if (shouldBeDark) {
      document.documentElement.setAttribute('data-theme', 'dark');
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.removeAttribute('data-theme');
      document.documentElement.classList.remove('dark');
    }
  }

  // Инициализируем тему сразу
  initTheme();

  // Слушаем изменения системной темы
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', function() {
      const currentTheme = localStorage.getItem(STORAGE_KEY) || 'system';
      if (currentTheme === 'system') {
        initTheme();
      }
    });
  }

  // Слушаем изменения в localStorage (для синхронизации между вкладками)
  window.addEventListener('storage', function(e) {
    if (e.key === STORAGE_KEY) {
      initTheme();
    }
  });
})();